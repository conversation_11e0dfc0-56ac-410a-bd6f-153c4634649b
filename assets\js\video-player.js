// Video Player Page functionality
class VideoPlayer {
    constructor(app) {
        this.app = app;
        this.currentVideo = null;
        this.videoId = this.getVideoIdFromURL();
        
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        if (this.videoId) {
            this.loadVideo(this.videoId);
        } else {
            this.showError('Video not found');
        }
        
        this.setupEventListeners();
    }
    
    getVideoIdFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('id');
    }
    
    loadVideo(videoId) {
        this.currentVideo = this.app.getVideoById(videoId);
        
        if (!this.currentVideo) {
            this.showError('Video not found');
            return;
        }
        
        this.displayVideo();
        this.loadComments();
        this.loadRelatedVideos();
        this.loadOtherVideos();
        this.updatePageTitle();
    }
    
    displayVideo() {
        const video = this.currentVideo;
        
        // Update video player
        const videoPlayer = document.getElementById('videoPlayer');
        if (videoPlayer) {
            videoPlayer.src = video.videoUrl;
            videoPlayer.poster = video.thumbnail;
        }
        
        // Update video info
        const videoTitle = document.getElementById('videoTitle');
        if (videoTitle) videoTitle.textContent = video.title;
        
        const videoViews = document.getElementById('videoViews');
        if (videoViews) videoViews.textContent = `${this.app.formatNumber(video.views)} views`;
        
        const uploadDate = document.getElementById('uploadDate');
        if (uploadDate) uploadDate.textContent = this.app.formatDate(video.uploadDate);
        
        const videoDuration = document.getElementById('videoDuration');
        if (videoDuration) videoDuration.textContent = video.duration;
        
        const videoDescription = document.getElementById('videoDescription');
        if (videoDescription) videoDescription.textContent = video.description;
        
        // Update like count
        const likeCount = document.getElementById('likeCount');
        if (likeCount) likeCount.textContent = this.app.formatNumber(video.likes);
        
        // Update tags
        this.displayTags();
        
        // Update uploader info
        this.displayUploaderInfo();
        
        // Update action buttons state
        this.updateActionButtons();
    }
    
    displayTags() {
        const videoTags = document.getElementById('videoTags');
        if (!videoTags) return;
        
        videoTags.innerHTML = '';
        
        this.currentVideo.tags.forEach(tag => {
            const tagElement = document.createElement('a');
            tagElement.className = 'tag';
            tagElement.href = '#';
            tagElement.textContent = `#${tag}`;
            tagElement.addEventListener('click', (e) => {
                e.preventDefault();
                this.searchByTag(tag);
            });
            videoTags.appendChild(tagElement);
        });
    }
    
    displayUploaderInfo() {
        const uploaderInfo = document.getElementById('uploaderInfo');
        if (!uploaderInfo) return;
        
        const uploader = this.currentVideo.uploader;
        
        uploaderInfo.innerHTML = `
            <img src="${uploader.avatar}" alt="${uploader.username}" class="uploader-avatar-large">
            <div class="uploader-details">
                <h4>${uploader.username}</h4>
                <p>Uploaded ${this.app.formatDate(this.currentVideo.uploadDate)}</p>
            </div>
        `;
    }
    
    updateActionButtons() {
        const likeBtn = document.getElementById('likeBtn');
        const favoriteBtn = document.getElementById('favoriteBtn');
        
        if (likeBtn) {
            const isLiked = this.app.likes.includes(this.currentVideo.id);
            likeBtn.classList.toggle('active', isLiked);
        }
        
        if (favoriteBtn) {
            const isFavorited = this.app.favorites.includes(this.currentVideo.id);
            favoriteBtn.classList.toggle('active', isFavorited);
        }
    }
    
    setupEventListeners() {
        // Like button
        const likeBtn = document.getElementById('likeBtn');
        if (likeBtn) {
            likeBtn.addEventListener('click', () => {
                const isLiked = this.app.toggleLike(this.currentVideo.id);
                likeBtn.classList.toggle('active', isLiked);
                
                // Update like count (simulate)
                const likeCount = document.getElementById('likeCount');
                if (likeCount) {
                    const currentLikes = parseInt(likeCount.textContent.replace(/[^\d]/g, ''));
                    const newLikes = isLiked ? currentLikes + 1 : currentLikes - 1;
                    likeCount.textContent = this.app.formatNumber(newLikes);
                }
            });
        }
        
        // Favorite button
        const favoriteBtn = document.getElementById('favoriteBtn');
        if (favoriteBtn) {
            favoriteBtn.addEventListener('click', () => {
                const isFavorited = this.app.toggleFavorite(this.currentVideo.id);
                favoriteBtn.classList.toggle('active', isFavorited);
            });
        }
        
        // Share button
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.openShareModal();
            });
        }
        
        // Comment form
        const commentForm = document.getElementById('commentForm');
        if (commentForm) {
            commentForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitComment();
            });
        }
        
        // Copy link button
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', () => {
                this.copyVideoLink();
            });
        }
    }
    
    loadComments() {
        const commentsList = document.getElementById('commentsList');
        if (!commentsList) return;
        
        const comments = this.app.getCommentsForVideo(this.currentVideo.id);
        commentsList.innerHTML = '';
        
        if (comments.length === 0) {
            commentsList.innerHTML = '<p class="no-comments">No comments yet. Be the first to comment!</p>';
            return;
        }
        
        comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            commentsList.appendChild(commentElement);
        });
    }
    
    createCommentElement(comment) {
        const commentDiv = document.createElement('div');
        commentDiv.className = 'comment';
        
        commentDiv.innerHTML = `
            <img src="${comment.userAvatar}" alt="${comment.username}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-header">
                    <span class="comment-username">${comment.username}</span>
                    <span class="comment-timestamp">${this.app.formatDate(comment.timestamp)}</span>
                </div>
                <p class="comment-text">${comment.comment}</p>
                <div class="comment-actions">
                    <button class="comment-action">👍 ${comment.likes}</button>
                    <button class="comment-action">Reply</button>
                </div>
            </div>
        `;
        
        return commentDiv;
    }
    
    submitComment() {
        const commentInput = document.getElementById('commentInput');
        if (!commentInput) return;
        
        const commentText = commentInput.value.trim();
        if (!commentText) return;
        
        // Create new comment (simulate)
        const newComment = {
            id: 'c' + Date.now(),
            videoId: this.currentVideo.id,
            userId: 'current_user',
            username: 'You',
            userAvatar: 'assets/images/default-avatar.jpg',
            comment: commentText,
            timestamp: new Date().toISOString(),
            likes: 0
        };
        
        // Add to comments array
        this.app.comments.push(newComment);
        
        // Clear input
        commentInput.value = '';
        
        // Reload comments
        this.loadComments();
        
        // Show success message
        this.showSuccessMessage('Comment posted successfully!');
    }
    
    loadRelatedVideos() {
        const relatedVideos = document.getElementById('relatedVideos');
        if (!relatedVideos) return;
        
        const related = this.app.getRelatedVideos(this.currentVideo, 4);
        relatedVideos.innerHTML = '';
        
        related.forEach(video => {
            const videoElement = this.createSidebarVideo(video);
            relatedVideos.appendChild(videoElement);
        });
    }
    
    loadOtherVideos() {
        const otherVideos = document.getElementById('otherVideos');
        if (!otherVideos) return;
        
        // Get random videos excluding current and related
        const relatedIds = this.app.getRelatedVideos(this.currentVideo).map(v => v.id);
        const others = this.app.videos
            .filter(v => v.id !== this.currentVideo.id && !relatedIds.includes(v.id))
            .sort(() => Math.random() - 0.5)
            .slice(0, 4);
        
        otherVideos.innerHTML = '';
        
        others.forEach(video => {
            const videoElement = this.createSidebarVideo(video);
            otherVideos.appendChild(videoElement);
        });
    }
    
    createSidebarVideo(video) {
        const videoDiv = document.createElement('a');
        videoDiv.className = 'sidebar-video';
        videoDiv.href = `video.html?id=${video.id}`;
        
        videoDiv.innerHTML = `
            <div class="sidebar-video-thumbnail">
                <img src="${video.thumbnail}" alt="${video.title}">
                <span class="sidebar-video-duration">${video.duration}</span>
            </div>
            <div class="sidebar-video-info">
                <h4 class="sidebar-video-title">${video.title}</h4>
                <div class="sidebar-video-meta">
                    <span>${video.uploader.username}</span>
                    <span>${this.app.formatNumber(video.views)} views</span>
                </div>
            </div>
        `;
        
        return videoDiv;
    }
    
    searchByTag(tag) {
        // Redirect to search results for this tag
        window.location.href = `../index.html?search=${encodeURIComponent(tag)}`;
    }
    
    openShareModal() {
        const shareModal = document.getElementById('shareModal');
        const shareLinkInput = document.getElementById('shareLinkInput');
        
        if (shareModal && shareLinkInput) {
            shareLinkInput.value = window.location.href;
            this.app.openModal('shareModal');
        }
    }
    
    copyVideoLink() {
        const shareLinkInput = document.getElementById('shareLinkInput');
        if (shareLinkInput) {
            shareLinkInput.select();
            document.execCommand('copy');
            this.showSuccessMessage('Link copied to clipboard!');
        }
    }
    
    updatePageTitle() {
        if (this.currentVideo) {
            document.title = `${this.currentVideo.title} - PornTubeX`;
        }
    }
    
    showError(message) {
        console.error(message);
        alert(message);
    }
    
    showSuccessMessage(message) {
        // Simple success message - you could implement a toast system
        console.log(message);
        // For now, just show an alert
        alert(message);
    }
}

// Initialize video player when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initVideoPlayer = () => {
        if (window.app && window.app.videos.length > 0) {
            new VideoPlayer(window.app);
        } else {
            setTimeout(initVideoPlayer, 100);
        }
    };
    
    initVideoPlayer();
});
