<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category - PornTubeX</title>
    <meta name="description" content="Browse videos by category on PornTubeX">
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="../index.html" class="brand-link">
                        <h1 class="brand-title">PornTubeX</h1>
                    </a>
                </div>
                
                <div class="nav-search">
                    <form class="search-form" id="searchForm">
                        <input type="text" class="search-input" placeholder="Search videos..." id="searchInput">
                        <button type="submit" class="search-btn">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>
                
                <div class="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="#" class="nav-link">Browse</a>
                    <a href="#" class="nav-link active">Categories</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main-content category-page">
        <div class="container">
            <!-- Category Header -->
            <div class="category-header">
                <h1 class="category-title" id="categoryTitle">Loading...</h1>
                <p class="category-description" id="categoryDescription">Loading...</p>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <div class="filters-container">
                    <div class="filter-group">
                        <label for="sortSelect">Sort by:</label>
                        <select class="filter-select" id="sortSelect">
                            <option value="recent">Most Recent</option>
                            <option value="popular">Most Popular</option>
                            <option value="views">Most Viewed</option>
                            <option value="rating">Highest Rated</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label for="subcategorySelect">Subcategory:</label>
                        <select class="filter-select" id="subcategorySelect">
                            <option value="all">All</option>
                            <!-- Subcategories will be loaded dynamically -->
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <button class="filter-btn active" data-duration="all">All Durations</button>
                        <button class="filter-btn" data-duration="short">Under 10 min</button>
                        <button class="filter-btn" data-duration="medium">10-20 min</button>
                        <button class="filter-btn" data-duration="long">Over 20 min</button>
                    </div>
                </div>
            </div>

            <!-- Videos Grid -->
            <div class="videos-section">
                <div class="videos-grid" id="categoryVideos">
                    <!-- Videos will be loaded dynamically -->
                </div>
                
                <!-- Load More Button -->
                <div class="load-more-container">
                    <button class="load-more-btn" id="loadMoreBtn">Load More Videos</button>
                </div>
            </div>

            <!-- No Results -->
            <div class="no-results" id="noResults" style="display: none;">
                <h3>No videos found</h3>
                <p>Try adjusting your filters or search for different content.</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>PornTubeX</h4>
                    <p>Premium adult content platform</p>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li><a href="#">Romantic</a></li>
                        <li><a href="#">Artistic</a></li>
                        <li><a href="#">Wellness</a></li>
                        <li><a href="#">Massage</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 PornTubeX. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../assets/js/data-adapter.js"></script>
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/category.js"></script>
</body>
</html>
