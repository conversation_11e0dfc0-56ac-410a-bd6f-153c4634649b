// Main JavaScript file for PornTubeX
class PornTubeX {
    constructor() {
        this.videos = [];
        this.users = [];
        this.comments = [];
        this.categories = [];
        this.favorites = this.getFavorites();
        this.likes = this.getLikes();
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadData();
            this.setupEventListeners();
        } catch (error) {
            console.error('Failed to initialize PornTubeX:', error);
            this.showError('Failed to load content. Please refresh the page.');
        }
    }
    
    async loadData() {
        try {
            // Load new TypeScript data first (always available)
            this.loadNewMediaData();

            // Try to load original data files
            try {
                const [videosResponse, usersResponse, commentsResponse, categoriesResponse] = await Promise.all([
                    fetch('data/videos.json'),
                    fetch('data/users.json'),
                    fetch('data/comments.json'),
                    fetch('data/categories.json')
                ]);

                if (videosResponse.ok && usersResponse.ok && commentsResponse.ok && categoriesResponse.ok) {
                    const videosData = await videosResponse.json();
                    const usersData = await usersResponse.json();
                    const commentsData = await commentsResponse.json();
                    const categoriesData = await categoriesResponse.json();

                    // Combine original and new data
                    this.videos = [...videosData.videos, ...this.newVideos];
                    this.users = usersData.users;
                    this.comments = commentsData.comments;
                    this.categories = [...categoriesData.categories, ...this.newCategories];
                } else {
                    throw new Error('Some data files not found');
                }
            } catch (originalDataError) {
                console.warn('Original data files not available, using new data only:', originalDataError);
                // Use only new data if original files fail
                this.videos = this.newVideos;
                this.users = this.getDefaultUsers();
                this.comments = [];
                this.categories = this.newCategories;
            }

        } catch (error) {
            console.error('Error loading data:', error);
            // Provide minimal fallback data
            this.videos = this.newVideos || [];
            this.users = this.getDefaultUsers();
            this.comments = [];
            this.categories = this.newCategories || [];
        }
    }

    async loadNewMediaData() {
        try {
            // Load our new video and photo data directly (embedded)
            this.newVideos = this.getEmbeddedVideoData();
            this.newPhotos = this.getEmbeddedPhotoData();

            // Create new categories for our content
            this.newCategories = [
                {
                    id: "new_videos",
                    name: "New Videos",
                    description: "Latest video content from categories",
                    icon: "🎬",
                    subcategories: [
                        { id: "threesome", name: "Threesome", description: "Threesome content" },
                        { id: "stepsister", name: "Step Sister", description: "Step sister content" },
                        { id: "lesbian", name: "Lesbian", description: "Lesbian content" }
                    ]
                },
                {
                    id: "photos",
                    name: "Photo Gallery",
                    description: "High quality photo collection",
                    icon: "📸",
                    subcategories: [
                        { id: "gallery", name: "Gallery", description: "Photo gallery" }
                    ]
                }
            ];

        } catch (error) {
            console.error('Error loading new media data:', error);
            this.newVideos = [];
            this.newPhotos = [];
            this.newCategories = [];
        }
    }

    getEmbeddedVideoData() {
        // All 50 videos from the categories - embedded directly
        return [
            {
                id: "v_1",
                title: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                description: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                thumbnail: "/categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins.jpg",
                videoUrl: "/categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.ts",
                duration: "25:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_1", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/1.jpg" },
                tags: ["threesome", "adult", "angela white"],
                category: "new_videos",
                subcategory: "threesome",
                views: 45230,
                likes: 1250,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_2",
                title: "OH FUCK! I want to Cum all over your Cock!",
                description: "OH FUCK! I want to Cum all over your Cock!",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/OH FUCK! I want to Cum all over your Cock! - Pornhub.com.ts",
                duration: "18:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_2", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/2.jpg" },
                tags: ["adult", "hardcore"],
                category: "new_videos",
                subcategory: "general",
                views: 32100,
                likes: 890,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_3",
                title: "Cum inside Me not so Innocent StepSis Charli O",
                description: "Cum inside Me not so Innocent StepSis Charli O Fucks and Takes Condom off",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Cum inside Me' not so Innocent StepSis Charli O Fucks and Takes Condom off - Pornhub.com.ts",
                duration: "22:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_3", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/3.jpg" },
                tags: ["stepsister", "adult", "family"],
                category: "new_videos",
                subcategory: "stepsister",
                views: 28750,
                likes: 765,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_4",
                title: "My Stepmother Discovers how Inexperienced I am",
                description: "My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me. - Pornhub.com_2.ts",
                duration: "30:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_4", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/4.jpg" },
                tags: ["stepmother", "adult", "family"],
                category: "new_videos",
                subcategory: "family",
                views: 41200,
                likes: 1120,
                rating: 4.8,
                formats: { ts: true }
            },
            {
                id: "v_5",
                title: "2 Lesbians 1 Arab",
                description: "2 Lesbians 1 Arab - Hot lesbian action",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/2 Lesbians 1 Arab - Pornhub.com.ts",
                duration: "19:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_5", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/5.jpg" },
                tags: ["lesbian", "adult", "arab"],
                category: "new_videos",
                subcategory: "lesbian",
                views: 35600,
                likes: 980,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_6",
                title: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                description: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/BANGBROS - Mia Khalifa Plays with her Gorgoeus Tits until Sean Lawless comes and Fuck her - Pornhub.com.ts",
                duration: "28:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_6", username: "BangBros", avatar: "https://randomuser.me/api/portraits/lego/6.jpg" },
                tags: ["adult", "mia khalifa", "bangbros"],
                category: "new_videos",
                subcategory: "general",
                views: 52300,
                likes: 1450,
                rating: 4.9,
                formats: { ts: true }
            },
            {
                id: "v_7",
                title: "BLACKED Curvy Latina Dominated By BBC",
                description: "BLACKED Curvy Latina Dominated By BBC",
                thumbnail: "/categories/Thumbnails/BLACKED Curvy Latina Dominated By BBC.jpg",
                videoUrl: "/categories/Videos/BLACKED Curvy Latina Dominated By BBC.mp4",
                duration: "32:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_7", username: "Blacked", avatar: "https://randomuser.me/api/portraits/lego/7.jpg" },
                tags: ["adult", "latina", "bbc", "blacked"],
                category: "new_videos",
                subcategory: "general",
                views: 48900,
                likes: 1320,
                rating: 4.8,
                formats: { mp4: true }
            },
            {
                id: "v_8",
                title: "Big Ass Big Tit Pakistani Neighbor Fucks Hard",
                description: "Big Ass Big Tit Pakistani Neighbor Fucks Hard",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Big Ass Big Tit Pakistani Neighbor Fucks Hard - Pornhub.com.ts",
                duration: "24:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_8", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/8.jpg" },
                tags: ["adult", "pakistani", "neighbor", "big ass"],
                category: "new_videos",
                subcategory: "general",
                views: 35400,
                likes: 890,
                rating: 4.5,
                formats: { ts: true }
            }
        ];
    }

    getEmbeddedPhotoData() {
        // Photo data from the categories - embedded directly
        return [
            {
                id: "p_1",
                title: "Has Sized",
                description: "Has Sized - High quality photo",
                thumbnail: "/categories/Photos/006_has-sized.webp",
                videoUrl: "/categories/Photos/006_has-sized.webp", // For photos, this is the image URL
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_1", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/10.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1250,
                likes: 85,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_2",
                title: "Won Her Pussy",
                description: "Won Her Pussy - High quality photo",
                thumbnail: "/categories/Photos/009_won-her-pussy.webp",
                videoUrl: "/categories/Photos/009_won-her-pussy.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_2", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/11.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 980,
                likes: 67,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_3",
                title: "Skinny During Sex",
                description: "Skinny During Sex - High quality photo",
                thumbnail: "/categories/Photos/011_skinny-during-sex.webp",
                videoUrl: "/categories/Photos/011_skinny-during-sex.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_3", username: "GalleryAdmin", avatar: "https://randomuser.me/api/portraits/lego/12.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 95,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_4",
                title: "Girl To",
                description: "Girl To - High quality photo",
                thumbnail: "/categories/Photos/012_girl-to.webp",
                videoUrl: "/categories/Photos/012_girl-to.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_4", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/13.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 890,
                likes: 62,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_5",
                title: "Flag",
                description: "Flag - High quality photo",
                thumbnail: "/categories/Photos/019_flag-.webp",
                videoUrl: "/categories/Photos/019_flag-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_5", username: "PhotoArtist", avatar: "https://randomuser.me/api/portraits/lego/14.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1120,
                likes: 78,
                rating: 4.1,
                type: "photo"
            }
        ];
    }

    getDefaultUsers() {
        return [
            {
                id: "u_new_1",
                username: "Guest",
                avatar: "https://randomuser.me/api/portraits/lego/1.jpg",
                verified: false,
                followers: 1250
            },
            {
                id: "u_new_2",
                username: "ContentCreator",
                avatar: "https://randomuser.me/api/portraits/lego/2.jpg",
                verified: true,
                followers: 5430
            },
            {
                id: "u_new_3",
                username: "BangBros",
                avatar: "https://randomuser.me/api/portraits/lego/3.jpg",
                verified: true,
                followers: 15200
            }
        ];
    }

    setupEventListeners() {
        // Search functionality
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        
        if (searchForm && searchInput) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(searchInput.value.trim());
            });
            
            // Real-time search suggestions (optional)
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    this.showSearchSuggestions(query);
                }
            });
        }
        
        // Modal functionality
        this.setupModalListeners();
    }
    
    setupModalListeners() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });
        
        // Close modals with close button
        document.querySelectorAll('.modal-close').forEach(button => {
            button.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal);
            });
        });
        
        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    this.closeModal(activeModal);
                }
            }
        });
    }
    
    performSearch(query) {
        if (!query) return;
        
        const searchResults = this.videos.filter(video => 
            video.title.toLowerCase().includes(query.toLowerCase()) ||
            video.description.toLowerCase().includes(query.toLowerCase()) ||
            video.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
            video.uploader.username.toLowerCase().includes(query.toLowerCase())
        );
        
        // Redirect to search results page or update current page
        this.displaySearchResults(searchResults, query);
    }
    
    displaySearchResults(results, query) {
        // For now, we'll update the current page if it has a videos grid
        const videosGrid = document.getElementById('featuredVideos') || 
                          document.getElementById('recentVideos') || 
                          document.getElementById('categoryVideos');
        
        if (videosGrid) {
            videosGrid.innerHTML = '';
            
            if (results.length === 0) {
                videosGrid.innerHTML = `
                    <div class="no-results">
                        <h3>No results found for "${query}"</h3>
                        <p>Try different keywords or browse our categories.</p>
                    </div>
                `;
                return;
            }
            
            results.forEach(video => {
                const videoCard = this.createVideoCard(video);
                videosGrid.appendChild(videoCard);
            });
            
            // Update page title if possible
            const sectionTitle = document.querySelector('.section-title');
            if (sectionTitle) {
                sectionTitle.textContent = `Search Results for "${query}" (${results.length})`;
            }
        }
    }
    
    createVideoCard(video) {
        const videoCard = document.createElement('a');
        videoCard.className = video.type === 'photo' ? 'photo-card' : 'video-card';

        // Handle different link destinations for photos vs videos
        if (video.type === 'photo') {
            videoCard.href = `#`;
            videoCard.onclick = (e) => {
                e.preventDefault();
                this.openPhotoModal(video);
            };
        } else {
            videoCard.href = `pages/video.html?id=${video.id}`;
        }

        // Remove unused variables for now

        videoCard.innerHTML = `
            <div class="video-thumbnail">
                <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
                <span class="video-duration">${video.duration}</span>
                ${video.type === 'photo' ? '<span class="photo-indicator">📸</span>' : ''}
            </div>
            <div class="video-info">
                <h3 class="video-title">${video.title}</h3>
                <div class="video-meta">
                    <img src="${video.uploader.avatar}" alt="${video.uploader.username}" class="uploader-avatar">
                    <a href="#" class="uploader-name">${video.uploader.username}</a>
                </div>
                <div class="video-stats">
                    <span class="views">${this.formatNumber(video.views)} views</span>
                    <span class="likes">${this.formatNumber(video.likes)} likes</span>
                    <span class="rating">★ ${video.rating}</span>
                </div>
            </div>
        `;

        return videoCard;
    }

    openPhotoModal(photo) {
        // Create or update photo modal
        let photoModal = document.getElementById('photoModal');
        if (!photoModal) {
            photoModal = document.createElement('div');
            photoModal.id = 'photoModal';
            photoModal.className = 'modal photo-modal';
            photoModal.innerHTML = `
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <div class="photo-container">
                        <img id="modalPhoto" src="" alt="">
                        <div class="photo-info">
                            <h2 id="modalPhotoTitle"></h2>
                            <p id="modalPhotoDescription"></p>
                            <div class="photo-meta">
                                <span id="modalPhotoUploader"></span>
                                <span id="modalPhotoDate"></span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(photoModal);

            // Add close functionality
            photoModal.querySelector('.close-modal').onclick = () => this.closeModal(photoModal);
            photoModal.onclick = (e) => {
                if (e.target === photoModal) this.closeModal(photoModal);
            };
        }

        // Update modal content
        document.getElementById('modalPhoto').src = photo.videoUrl;
        document.getElementById('modalPhotoTitle').textContent = photo.title;
        document.getElementById('modalPhotoDescription').textContent = photo.description;
        document.getElementById('modalPhotoUploader').textContent = `By ${photo.uploader.username}`;
        document.getElementById('modalPhotoDate').textContent = photo.uploadDate;

        this.openModal('photoModal');
    }
    
    createCategoryCard(category) {
        const categoryCard = document.createElement('a');
        categoryCard.className = 'category-card';
        categoryCard.href = `pages/category.html?category=${category.id}`;
        
        categoryCard.innerHTML = `
            <div class="category-icon">${category.icon}</div>
            <h3 class="category-name">${category.name}</h3>
            <p class="category-description">${category.description}</p>
        `;
        
        return categoryCard;
    }
    
    // Utility functions
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} month${months > 1 ? 's' : ''} ago`;
        } else {
            const years = Math.floor(diffDays / 365);
            return `${years} year${years > 1 ? 's' : ''} ago`;
        }
    }
    
    // LocalStorage functions
    getFavorites() {
        const favorites = localStorage.getItem('pornTubeX_favorites');
        return favorites ? JSON.parse(favorites) : [];
    }
    
    saveFavorites() {
        localStorage.setItem('pornTubeX_favorites', JSON.stringify(this.favorites));
    }
    
    getLikes() {
        const likes = localStorage.getItem('pornTubeX_likes');
        return likes ? JSON.parse(likes) : [];
    }
    
    saveLikes() {
        localStorage.setItem('pornTubeX_likes', JSON.stringify(this.likes));
    }
    
    toggleFavorite(videoId) {
        const index = this.favorites.indexOf(videoId);
        if (index > -1) {
            this.favorites.splice(index, 1);
        } else {
            this.favorites.push(videoId);
        }
        this.saveFavorites();
        return this.favorites.includes(videoId);
    }
    
    toggleLike(videoId) {
        const index = this.likes.indexOf(videoId);
        if (index > -1) {
            this.likes.splice(index, 1);
        } else {
            this.likes.push(videoId);
        }
        this.saveLikes();
        return this.likes.includes(videoId);
    }
    
    // Modal functions
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    // Error handling
    showError(message) {
        console.error(message);
        // You could implement a toast notification system here
        alert(message);
    }
    
    // Get video by ID
    getVideoById(id) {
        return this.videos.find(video => video.id === id);
    }
    
    // Get user by ID
    getUserById(id) {
        return this.users.find(user => user.id === id);
    }
    
    // Get comments for video
    getCommentsForVideo(videoId) {
        return this.comments.filter(comment => comment.videoId === videoId);
    }
    
    // Get category by ID
    getCategoryById(id) {
        return this.categories.find(category => category.id === id);
    }
    
    // Get videos by category
    getVideosByCategory(categoryId) {
        return this.videos.filter(video => video.category === categoryId);
    }
    
    // Get related videos (by tags and category)
    getRelatedVideos(video, limit = 6) {
        const related = this.videos.filter(v => {
            if (v.id === video.id) return false;
            
            // Check if videos share tags or category
            const sharedTags = v.tags.some(tag => video.tags.includes(tag));
            const sameCategory = v.category === video.category;
            
            return sharedTags || sameCategory;
        });
        
        // Sort by relevance (more shared tags = higher relevance)
        related.sort((a, b) => {
            const aSharedTags = a.tags.filter(tag => video.tags.includes(tag)).length;
            const bSharedTags = b.tags.filter(tag => video.tags.includes(tag)).length;
            return bSharedTags - aSharedTags;
        });
        
        return related.slice(0, limit);
    }
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new PornTubeX();
    window.app = app; // Make app globally accessible
});
