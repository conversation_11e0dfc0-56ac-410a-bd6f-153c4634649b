<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PornTubeX Demo - Test All Features</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .demo-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .demo-link:hover {
            background: #0056b3;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🎬 PornTubeX Demo</h1>
    <p>Welcome to the PornTubeX demonstration. This page provides links to test all features of the website.</p>
    
    <div class="status success">
        <strong>✅ Website Status:</strong> All components are functional and ready for testing.
    </div>
    
    <div class="demo-section">
        <h2>🏠 Main Pages</h2>
        <p>Test the core pages of the website:</p>
        <a href="index.html" class="demo-link">Homepage</a>
        <a href="pages/video.html?id=v001" class="demo-link">Video Player (Sample)</a>
        <a href="pages/category.html?category=romantic" class="demo-link">Category Page (Romantic)</a>
    </div>
    
    <div class="demo-section">
        <h2>🎥 Video Player Features</h2>
        <p>Test specific video player functionality:</p>
        <a href="pages/video.html?id=v001" class="demo-link">Romantic Video</a>
        <a href="pages/video.html?id=v002" class="demo-link">Massage Video</a>
        <a href="pages/video.html?id=v003" class="demo-link">Artistic Video</a>
        <a href="pages/video.html?id=v004" class="demo-link">Intimate Video</a>
        <a href="pages/video.html?id=v005" class="demo-link">Wellness Video</a>
        <a href="pages/video.html?id=v006" class="demo-link">Dance Video</a>
    </div>
    
    <div class="demo-section">
        <h2>📂 Category Pages</h2>
        <p>Browse content by category:</p>
        <a href="pages/category.html?category=romantic" class="demo-link">Romantic</a>
        <a href="pages/category.html?category=massage" class="demo-link">Massage</a>
        <a href="pages/category.html?category=artistic" class="demo-link">Artistic</a>
        <a href="pages/category.html?category=wellness" class="demo-link">Wellness</a>
    </div>
    
    <div class="demo-section">
        <h2>🧪 Interactive Features to Test</h2>
        <ul class="feature-list">
            <li>Search functionality (try searching for "romantic", "massage", or "dance")</li>
            <li>Like/favorite buttons (data persists in localStorage)</li>
            <li>Comment submission (adds comments dynamically)</li>
            <li>Share functionality (copy video links)</li>
            <li>Tag clicking (filters videos by tags)</li>
            <li>Category filtering and sorting</li>
            <li>Related videos recommendations</li>
            <li>Responsive design (resize browser window)</li>
            <li>Video player controls</li>
            <li>Modal dialogs (share modal)</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h2>📱 Responsive Testing</h2>
        <p>Test the responsive design:</p>
        <div class="status info">
            <strong>💡 Tip:</strong> Use browser developer tools to simulate different device sizes:
            <br>• Desktop (1200px+)
            <br>• Tablet (768px - 1024px)
            <br>• Mobile (320px - 768px)
        </div>
    </div>
    
    <div class="demo-section">
        <h2>🛠️ Developer Tools</h2>
        <p>Additional utilities for development:</p>
        <a href="generate-placeholders.html" class="demo-link">Generate Placeholder Images</a>
        <a href="assets/images/placeholder-generator.html" class="demo-link">Image Placeholder Generator</a>
    </div>
    
    <div class="demo-section">
        <h2>📊 Sample Data</h2>
        <p>The website includes:</p>
        <ul class="feature-list">
            <li>6 sample videos with full metadata</li>
            <li>8 user profiles with avatars</li>
            <li>8 sample comments with timestamps</li>
            <li>4 content categories with subcategories</li>
            <li>Working video players with sample content</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h2>🔧 Testing Checklist</h2>
        <p>Use this checklist to verify all features work correctly:</p>
        <ul class="feature-list">
            <li>Homepage loads with featured and recent videos</li>
            <li>Category navigation works</li>
            <li>Search returns relevant results</li>
            <li>Video player page displays correctly</li>
            <li>Video controls function properly</li>
            <li>Like/favorite buttons toggle state</li>
            <li>Comments can be submitted</li>
            <li>Share modal opens and copy link works</li>
            <li>Tags are clickable and filter content</li>
            <li>Related videos appear in sidebar</li>
            <li>Category pages filter and sort correctly</li>
            <li>Load more functionality works</li>
            <li>Responsive design adapts to screen size</li>
            <li>All links and navigation work</li>
            <li>Error handling works for missing content</li>
        </ul>
    </div>
    
    <div class="demo-section">
        <h2>📝 Notes</h2>
        <ul>
            <li><strong>LocalStorage:</strong> Likes and favorites are stored locally and persist between sessions</li>
            <li><strong>Sample Videos:</strong> Using publicly available sample videos for demonstration</li>
            <li><strong>Placeholder Images:</strong> Using placeholder.com for consistent image generation</li>
            <li><strong>Responsive Design:</strong> Optimized for mobile, tablet, and desktop viewing</li>
            <li><strong>Performance:</strong> Lazy loading and efficient CSS for optimal performance</li>
        </ul>
    </div>
    
    <script>
        // Simple demo functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('PornTubeX Demo Page Loaded');
            console.log('Available test features:');
            console.log('- Homepage with video grid');
            console.log('- Video player with full functionality');
            console.log('- Category browsing and filtering');
            console.log('- Search and tag-based filtering');
            console.log('- Interactive comments and likes');
            console.log('- Responsive design for all devices');
        });
    </script>
</body>
</html>
