// Homepage specific functionality
class Homepage {
    constructor(app) {
        this.app = app;
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        this.loadCategories();
        this.loadFeaturedVideos();
        this.loadRecentVideos();
    }
    
    loadCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid) return;
        
        categoriesGrid.innerHTML = '';
        
        this.app.categories.forEach(category => {
            const categoryCard = this.app.createCategoryCard(category);
            categoriesGrid.appendChild(categoryCard);
        });
    }
    
    loadFeaturedVideos() {
        const featuredVideos = document.getElementById('featuredVideos');
        if (!featuredVideos) return;
        
        featuredVideos.innerHTML = '';
        
        // Get top-rated videos as featured
        const featured = this.app.videos
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 6);
        
        featured.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            featuredVideos.appendChild(videoCard);
        });
    }
    
    loadRecentVideos() {
        const recentVideos = document.getElementById('recentVideos');
        if (!recentVideos) return;
        
        recentVideos.innerHTML = '';
        
        // Get most recent videos
        const recent = this.app.videos
            .sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
            .slice(0, 8);
        
        recent.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            recentVideos.appendChild(videoCard);
        });
    }
}

// Initialize homepage when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initHomepage = () => {
        if (window.app && window.app.videos.length > 0) {
            new Homepage(window.app);
        } else {
            setTimeout(initHomepage, 100);
        }
    };
    
    initHomepage();
});
